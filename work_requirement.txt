#无人车一致性编队及围捕
##具体要求：
1、方形地图，四辆无人车位于地图角落，一辆敌方无人车位于地图中央，且随机游走躲避围捕
2、四辆无人车采用一致性控制协议完成编队控制及对敌方无人车的围捕
3、四辆无人车呈正方形编队，且敌方无人车无逃跑路径，即认定任务完成
4、应注意无人车避碰等实际物理约束

#Unmanned vehicle consistency formation and roundup
##Specific requirements:
1. Square map, four unmanned vehicles are located in the corners of the map, and one enemy unmanned vehicle is located in the center of the map, and randomly wanders to avoid roundups
2. The four unmanned vehicles use a consistent control protocol to complete formation control and round up the enemy unmanned vehicle
3. The four unmanned vehicles are in a square formation, and the enemy unmanned vehicle has no escape path, which means the mission is considered completed
4. Attention should be paid to actual physical constraints such as unmanned vehicle collision avoidance
