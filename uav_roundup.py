import pygame
import numpy as np
import random
import math

# --- 常量定义 ---
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 800
MAP_SIZE = 800

UAV_RADIUS = 10
ENEMY_RADIUS = 12
FRIENDLY_UAV_MAX_SPEED = 130
ENEMY_UAV_MAX_SPEED = 105

# 编队和捕获参数
TARGET_ROUNDUP_RADIUS_AROUND_ENEMY = ENEMY_RADIUS + UAV_RADIUS + ENEMY_RADIUS * 0.8 # 目标包围半径
INITIAL_FORMATION_RADIUS_FACTOR = 5.0 # 初始编队半径因子
FORMATION_SHRINK_RATE_PER_SEC_FACTOR = 0.35 # 编队收缩速率因子
IDEAL_CAPTURE_FORMATION_SIDE_LENGTH = TARGET_ROUNDUP_RADIUS_AROUND_ENEMY * math.sqrt(2)  # 理想捕获编队边长

# 成功条件参数
MAX_ACTUAL_ENCIRCLEMENT_RADIUS_DEVIATION = UAV_RADIUS * 2.0 # 最大包围半径偏差
FORMATION_SPAN_TOLERANCE = UAV_RADIUS * 4.0  # 编队跨度容忍度
MAX_ENEMY_OFFSET_FROM_FORMATION_CENTER = ENEMY_RADIUS * 2.5 # 敌方偏离编队中心的最大距离
ENEMY_TRAPPED_VELOCITY_THRESHOLD_FACTOR = 0.20 # 敌方被困速度阈值因子

# 友方UAV碰撞避免参数
FRIENDLY_COLLISION_AVOIDANCE_DIST = UAV_RADIUS * 2.5  # 友方碰撞避免距离
FRIENDLY_COLLISION_REPULSION_STRENGTH = 2.0  # 友方碰撞排斥强度
FRIENDLY_EXTREME_COLLISION_BOOST = 3.0  # 友方极端碰撞增强系数

# 友方-敌方碰撞避免参数
FRIENDLY_ENEMY_COLLISION_AVOIDANCE_DIST = UAV_RADIUS + ENEMY_RADIUS + 1 # 友方-敌方碰撞避免距离
FRIENDLY_ENEMY_COLLISION_REPULSION_STRENGTH = 1.0 # 友方-敌方碰撞排斥强度
FRIENDLY_ENEMY_REPULSION_WEIGHT = 0.2 # 友方-敌方排斥权重

# 地图边缘避免参数
MAP_EDGE_AVOIDANCE_DIST_FRIENDLY = UAV_RADIUS * 3.0 # 友方边缘避免距离
MAP_EDGE_REPULSION_STRENGTH_FRIENDLY = 1.0 # 友方边缘排斥强度
MAP_EDGE_AVOIDANCE_DIST_ENEMY = UAV_RADIUS * 2.0 # 敌方边缘避免距离
MAP_EDGE_REPULSION_STRENGTH_ENEMY = 0.5 # 敌方边缘排斥强度

# 敌方行为参数
ENEMY_WANDER_DIRECTION_CHANGE_INTERVAL = 2.0 # 敌方游荡方向变化间隔
ENEMY_EVASION_RADIUS_FROM_FRIENDLY = UAV_RADIUS * 8 # 敌方逃避友方的检测半径
ENEMY_EVASION_STRENGTH = 0.8 # 敌方逃避强度
ENEMY_MIN_VELOCITY_KICK_FACTOR = 0.1 # 敌方最小速度踢击因子
# 敌方恐慌行为参数
ENEMY_PANIC_THRESHOLD = 3 # 敌方恐慌阈值
ENEMY_PANIC_SPEED_BOOST = 1.2 # 敌方恐慌速度增强
ENEMY_ESCAPE_ATTEMPT_STRENGTH = 1.5 # 敌方逃离尝试强度
# 敌方中心寻求行为参数
ENEMY_CENTER_SEEK_THRESHOLD = UAV_RADIUS * 1.5 # 敌方中心寻求阈值
ENEMY_CENTER_SEEK_STRENGTH = 0.2 # 敌方中心寻求强度
# 敌方加速度限制参数
ENEMY_MAX_ACCELERATION = ENEMY_UAV_MAX_SPEED * 2.0 # 敌方最大加速度

# 颜色定义
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (220, 50, 50)
GREEN = (50, 205, 50)
DARK_GREEN_SUCCESS = (0, 100, 0)
GRAY = (200, 200, 200)

# 控制行为因子
FRIENDLY_UAV_STEERING_INTEGRATION_FACTOR = 0.35  # 友方UAV转向积分因子
FRIENDLY_ARRIVAL_RADIUS_FACTOR = 0.30  # 友方到达半径因子
FRIENDLY_MIN_ARRIVAL_SPEED_FACTOR = 0.40  # 友方最小到达速度因子

# 友方UAV行为权重
FORMATION_WEIGHT = 1.0 # 编队权重
FRIENDLY_REPULSION_WEIGHT = 0.8 # 友方排斥权重
EDGE_REPULSION_WEIGHT_FRIENDLY = 1.0 # 友方边缘排斥权重

# 围捕参数
CONTAINMENT_SQUEEZE_STRENGTH_FACTOR = 0.20 # 围捕挤压强度因子
CONTAINMENT_SQUEEZE_WEIGHT = 0.50      # 围捕挤压权重
# 友方加速度参数
FRIENDLY_MAX_ACCELERATION = FRIENDLY_UAV_MAX_SPEED * 6.0 # 友方最大加速度


# 辅助函数
def limit_vector(vector, max_length):
    """限制向量长度到最大值"""
    length = np.linalg.norm(vector)
    if length > max_length and length > 0:
        return (vector / length) * max_length
    return vector

def get_map_edge_repulsion(pos, radius, max_speed, map_s, avoidance_dist, repulsion_strength):
    """计算地图边缘排斥力"""
    repulsion = np.array([0.0, 0.0])
    if avoidance_dist <= 0: return repulsion
    dist_to_left_edge = pos[0] - radius
    if dist_to_left_edge < avoidance_dist:
        strength = (avoidance_dist - dist_to_left_edge) / avoidance_dist
        repulsion[0] += strength * max_speed * repulsion_strength
    dist_to_right_edge = map_s - (pos[0] + radius)
    if dist_to_right_edge < avoidance_dist:
        strength = (avoidance_dist - dist_to_right_edge) / avoidance_dist
        repulsion[0] -= strength * max_speed * repulsion_strength
    dist_to_top_edge = pos[1] - radius
    if dist_to_top_edge < avoidance_dist:
        strength = (avoidance_dist - dist_to_top_edge) / avoidance_dist
        repulsion[1] += strength * max_speed * repulsion_strength
    dist_to_bottom_edge = map_s - (pos[1] + radius)
    if dist_to_bottom_edge < avoidance_dist:
        strength = (avoidance_dist - dist_to_bottom_edge) / avoidance_dist
        repulsion[1] -= strength * max_speed * repulsion_strength
    return repulsion

# UAV基类
class UAV:
    def __init__(self, x, y, radius, color, max_speed, label=""):
        self.pos = np.array([x, y], dtype=float)
        self.vel = np.array([0, 0], dtype=float)
        self.radius = radius
        self.color = color
        self.max_speed = max_speed
        self.label = label

    def update_physics(self, dt):
        """更新物理状态"""
        self.pos += self.vel * dt
        self.pos[0] = np.clip(self.pos[0], self.radius, MAP_SIZE - self.radius)
        self.pos[1] = np.clip(self.pos[1], self.radius, MAP_SIZE - self.radius)

    def draw(self, screen, font=None):
        """绘制UAV"""
        pygame.draw.circle(screen, self.color, self.pos.astype(int), self.radius)
        pygame.draw.circle(screen, BLACK, self.pos.astype(int), self.radius, 1)
        if font and self.label:
            text_surf = font.render(self.label, True, BLACK)
            text_rect = text_surf.get_rect(center=self.pos.astype(int))
            screen.blit(text_surf, text_rect)

# 友方UAV类
class FriendlyUAV(UAV):
    def __init__(self, x, y, uav_id):
        super().__init__(x, y, UAV_RADIUS, GREEN, FRIENDLY_UAV_MAX_SPEED, label=f"F{uav_id}")
        self.id = uav_id
        self.prev_vel = np.array([0.0, 0.0])  # 用于加速度限制

    def update(self, dt, enemy_uav_obj, friendly_uavs, current_formation_target_radius, friendly_formation_center_calc): # 添加了友方编队中心计算
        self.compute_control_input(dt, enemy_uav_obj, friendly_uavs, current_formation_target_radius, friendly_formation_center_calc)
        self.update_physics(dt)

    def compute_control_input(self, dt, enemy_uav_obj, friendly_uavs, current_formation_target_radius, friendly_formation_center_calc): # 添加了友方编队中心计算
        enemy_pos = enemy_uav_obj.pos

        # 预测敌方未来位置进行智能围捕
        enemy_speed = np.linalg.norm(enemy_uav_obj.vel)
        prediction_time = 1.0  # 预测1秒后的位置

        if enemy_speed > 0.1:
            predicted_enemy_pos = enemy_pos + enemy_uav_obj.vel * prediction_time
        else:
            predicted_enemy_pos = enemy_pos.copy()

        # 确保预测位置在地图范围内
        margin = UAV_RADIUS * 3.0
        predicted_enemy_pos[0] = np.clip(predicted_enemy_pos[0], margin, MAP_SIZE - margin)
        predicted_enemy_pos[1] = np.clip(predicted_enemy_pos[1], margin, MAP_SIZE - margin)

        # 动态目标位置调整以实现更好的包围
        offset_component = current_formation_target_radius / math.sqrt(2) if current_formation_target_radius > 0 else 0
        base_offsets = [
            np.array([-offset_component, -offset_component]),
            np.array([offset_component, -offset_component]),
            np.array([offset_component, offset_component]),
            np.array([-offset_component, offset_component])
        ]

        # 计算基于预测位置的理想目标位置
        ideal_target_pos = predicted_enemy_pos + base_offsets[self.id]

        # 智能堵截和动态调整
        if current_formation_target_radius <= TARGET_ROUNDUP_RADIUS_AROUND_ENEMY * 1.1:
            # 当处于紧密编队时，实施智能堵截
            other_uavs = [uav for uav in friendly_uavs if uav.id != self.id]
            if other_uavs and enemy_speed > 0.1:
                # 预测敌方可能的逃离路径
                map_center = np.array([MAP_SIZE / 2, MAP_SIZE / 2])

                # 检查敌方是否朝向边缘移动
                enemy_to_center = map_center - enemy_pos
                enemy_vel_normalized = enemy_uav_obj.vel / (enemy_speed + 1e-6)

                # 如果敌方朝向边缘移动，进行堵截
                dot_product = np.dot(enemy_vel_normalized, enemy_to_center)
                if dot_product < 0:  # 敌方远离中心
                    # 计算堵截位置
                    intercept_time = 2.0  # 堵截时间
                    intercept_pos = enemy_pos + enemy_uav_obj.vel * intercept_time

                    # 确保堵截位置在地图内
                    margin = UAV_RADIUS * 2.0
                    intercept_pos[0] = np.clip(intercept_pos[0], margin, MAP_SIZE - margin)
                    intercept_pos[1] = np.clip(intercept_pos[1], margin, MAP_SIZE - margin)

                    # 调整目标位置进行堵截
                    to_intercept = intercept_pos - self.pos
                    if np.linalg.norm(to_intercept) > 0.1:
                        # 混合原目标位置和堵截位置
                        ideal_target_pos = 0.6 * ideal_target_pos + 0.4 * intercept_pos

            # 填补空隙的逻辑
            if other_uavs:
                other_positions = np.array([uav.pos for uav in other_uavs])
                other_centroid = np.mean(other_positions, axis=0)

                centroid_to_enemy = enemy_pos - other_centroid
                if np.linalg.norm(centroid_to_enemy) > 0.1:
                    centroid_to_enemy_normalized = centroid_to_enemy / np.linalg.norm(centroid_to_enemy)
                    gap_fill_pos = enemy_pos + centroid_to_enemy_normalized * current_formation_target_radius
                    ideal_target_pos = 0.8 * ideal_target_pos + 0.2 * gap_fill_pos

        target_pos = ideal_target_pos

        safe_margin_from_edge = self.radius * 5.0 # 边缘安全距离
        target_pos[0] = np.clip(target_pos[0], safe_margin_from_edge, MAP_SIZE - safe_margin_from_edge)
        target_pos[1] = np.clip(target_pos[1], safe_margin_from_edge, MAP_SIZE - safe_margin_from_edge)

        vec_to_target = target_pos - self.pos
        dist_to_target = np.linalg.norm(vec_to_target)
        desired_velocity_formation = np.array([0.0,0.0])
        if dist_to_target > 0.1:
            arrival_radius = self.max_speed * FRIENDLY_ARRIVAL_RADIUS_FACTOR
            min_speed = self.max_speed * FRIENDLY_MIN_ARRIVAL_SPEED_FACTOR

            # 增强的动态速度调整 - 更加激进
            dist_to_enemy = np.linalg.norm(self.pos - enemy_pos)

            if current_formation_target_radius > TARGET_ROUNDUP_RADIUS_AROUND_ENEMY * 3.0:
                # 初始接近阶段 - 更快速度
                base_speed_factor = 0.8
            elif current_formation_target_radius > TARGET_ROUNDUP_RADIUS_AROUND_ENEMY * 2.0:
                # 初始接近阶段 - 加快速度
                base_speed_factor = 0.9
            elif current_formation_target_radius > TARGET_ROUNDUP_RADIUS_AROUND_ENEMY * 1.5:
                # 中期接近阶段 - 全速
                base_speed_factor = 1.0
            else:
                # 最终包围阶段 - 根据敌方速度调整
                if enemy_speed > ENEMY_UAV_MAX_SPEED * 0.5:
                    base_speed_factor = 1.0  # 敌方快速移动时保持全速
                else:
                    base_speed_factor = 0.8  # 敌方慢速时适当减速

            # 基于距离的额外调制
            enemy_distance_factor = min(1.0, dist_to_enemy / (current_formation_target_radius * 1.5))
            enemy_distance_factor = max(0.3, enemy_distance_factor)  # 最小距离因子

            max_approach_speed = self.max_speed * base_speed_factor * enemy_distance_factor

            if dist_to_target < arrival_radius:
                speed = max_approach_speed * (dist_to_target / arrival_radius if arrival_radius > 0 else 1.0)
                speed = max(speed, min_speed)
            else:
                speed = max_approach_speed
            if dist_to_target > 0:
                desired_velocity_formation = (vec_to_target / dist_to_target) * speed
        steering_force_formation = desired_velocity_formation - self.vel

        repulsion_sum_friendly = np.array([0.0, 0.0])
        for other_uav in friendly_uavs:
            if other_uav.id == self.id:
                continue
            dist_vec = self.pos - other_uav.pos
            distance = np.linalg.norm(dist_vec)
            if 0 < distance < FRIENDLY_COLLISION_AVOIDANCE_DIST:
                strength_factor = (FRIENDLY_COLLISION_AVOIDANCE_DIST - distance) / FRIENDLY_COLLISION_AVOIDANCE_DIST if FRIENDLY_COLLISION_AVOIDANCE_DIST > 0 else 1.0
                repel_force_magnitude = strength_factor * self.max_speed * FRIENDLY_COLLISION_REPULSION_STRENGTH
                if distance < self.radius * 1.5:
                    repel_force_magnitude *= FRIENDLY_EXTREME_COLLISION_BOOST
                if distance > 0:
                    repulsion_sum_friendly += (dist_vec / distance) * repel_force_magnitude

        edge_repulsion_force = get_map_edge_repulsion(self.pos, self.radius, self.max_speed, MAP_SIZE,
                                                      MAP_EDGE_AVOIDANCE_DIST_FRIENDLY,
                                                      MAP_EDGE_REPULSION_STRENGTH_FRIENDLY)

        repulsion_from_enemy = np.array([0.0, 0.0])
        dist_vec_to_enemy = self.pos - enemy_pos
        dist_to_enemy = np.linalg.norm(dist_vec_to_enemy)
        if 0 < dist_to_enemy < FRIENDLY_ENEMY_COLLISION_AVOIDANCE_DIST:
            strength_factor = (FRIENDLY_ENEMY_COLLISION_AVOIDANCE_DIST - dist_to_enemy) / FRIENDLY_ENEMY_COLLISION_AVOIDANCE_DIST if FRIENDLY_ENEMY_COLLISION_AVOIDANCE_DIST > 0 else 1.0
            repel_force_magnitude = strength_factor * self.max_speed * FRIENDLY_ENEMY_COLLISION_REPULSION_STRENGTH
            if dist_to_enemy > 0 :
                 repulsion_from_enemy = (dist_vec_to_enemy / dist_to_enemy) * repel_force_magnitude

        # 围捕挤压力
        squeeze_force_component = np.array([0.0, 0.0])
        if current_formation_target_radius <= TARGET_ROUNDUP_RADIUS_AROUND_ENEMY * 1.05: # 如果编队应该紧密
            if friendly_formation_center_calc is not None: # 确保中心已计算并传递
                vec_to_friendly_center = friendly_formation_center_calc - self.pos
                dist_to_friendly_center = np.linalg.norm(vec_to_friendly_center)
                if dist_to_friendly_center > self.radius * 0.5: # 如果已在中心则避免极端力
                    # 向友方编队中心施加力
                    squeeze_force_component = (vec_to_friendly_center / dist_to_friendly_center) * self.max_speed * CONTAINMENT_SQUEEZE_STRENGTH_FACTOR


        total_steering_influence = (steering_force_formation * FORMATION_WEIGHT +
                                    repulsion_sum_friendly * FRIENDLY_REPULSION_WEIGHT +
                                    edge_repulsion_force * EDGE_REPULSION_WEIGHT_FRIENDLY +
                                    repulsion_from_enemy * FRIENDLY_ENEMY_REPULSION_WEIGHT +
                                    squeeze_force_component * CONTAINMENT_SQUEEZE_WEIGHT) # 添加挤压力

        effective_dt_factor = dt / (1/60.0) if (1/60.0) > 0 else 1.0
        desired_vel_change = total_steering_influence * FRIENDLY_UAV_STEERING_INTEGRATION_FACTOR * effective_dt_factor

        # 应用加速度限制以实现平滑运动
        if dt > 0:
            max_vel_change = FRIENDLY_MAX_ACCELERATION * dt
            if np.linalg.norm(desired_vel_change) > max_vel_change:
                desired_vel_change = limit_vector(desired_vel_change, max_vel_change)

            self.vel += desired_vel_change
            self.vel = limit_vector(self.vel, self.max_speed)
            self.prev_vel = self.vel.copy()
        else:
            self.vel += desired_vel_change
            self.vel = limit_vector(self.vel, self.max_speed)

# 敌方UAV类
class EnemyUAV(UAV):
    def __init__(self, x, y):
        super().__init__(x, y, ENEMY_RADIUS, RED, ENEMY_UAV_MAX_SPEED, label="E")
        self.wander_angle = random.uniform(0, 2 * math.pi)
        self.time_to_change_wander_direction = 0
        self.prev_vel = np.array([0.0, 0.0])  # 用于加速度限制

    def _find_best_escape_direction(self, nearby_threats):
        """寻找威胁密度最小且避免边缘的逃离方向"""
        if not nearby_threats:
            return np.array([1.0, 0.0])  # 默认方向

        # 测试多个逃离方向
        test_angles = np.linspace(0, 2 * math.pi, 16)  # 测试16个方向
        best_direction = None
        min_total_score = float('inf')
        map_center = np.array([MAP_SIZE / 2, MAP_SIZE / 2])

        for angle in test_angles:
            direction = np.array([math.cos(angle), math.sin(angle)])

            # 预测移动后的位置
            future_pos = self.pos + direction * self.max_speed * 2.0  # 预测2秒后位置

            # 计算威胁分数
            threat_score = 0
            for fuav in nearby_threats:
                to_threat = fuav.pos - self.pos
                threat_dist = np.linalg.norm(to_threat)
                if threat_dist > 0:
                    dot_product = np.dot(direction, to_threat / threat_dist)
                    if dot_product > 0:  # 朝向威胁移动
                        threat_score += (1.0 + dot_product) / (threat_dist + 1e-6)
                    else:  # 远离威胁移动
                        threat_score += 0.1 / (threat_dist + 1e-6)

            # 计算边缘惩罚分数
            edge_penalty = 0
            margin = UAV_RADIUS * 3.0
            if (future_pos[0] < margin or future_pos[0] > MAP_SIZE - margin or
                future_pos[1] < margin or future_pos[1] > MAP_SIZE - margin):
                edge_penalty = 2.0  # 强烈惩罚接近边缘的方向

            # 计算远离地图中心的惩罚
            dist_to_center = np.linalg.norm(future_pos - map_center)
            center_penalty = dist_to_center / (MAP_SIZE * 0.5) * 0.5  # 轻微惩罚远离中心

            total_score = threat_score + edge_penalty + center_penalty

            if total_score < min_total_score:
                min_total_score = total_score
                best_direction = direction

        return best_direction if best_direction is not None else np.array([1.0, 0.0])

    def update(self, dt, friendly_uavs):
        self.update_behavior(dt, friendly_uavs)
        self.update_physics(dt)

    def update_behavior(self, dt, friendly_uavs):
        self.time_to_change_wander_direction -= dt
        if self.time_to_change_wander_direction <= 0:
            self.wander_angle += random.uniform(-math.pi / 1.2, math.pi / 1.2)
            self.time_to_change_wander_direction = ENEMY_WANDER_DIRECTION_CHANGE_INTERVAL + random.uniform(-0.5, 0.5)
        # 增强的威胁评估和恐慌模式
        nearby_threats = [fuav for fuav in friendly_uavs if np.linalg.norm(self.pos - fuav.pos) < ENEMY_EVASION_RADIUS_FROM_FRIENDLY]
        num_nearby_threats = len(nearby_threats)

        # 被多个威胁包围时的恐慌模式
        is_panic_mode = num_nearby_threats >= ENEMY_PANIC_THRESHOLD
        speed_multiplier = ENEMY_PANIC_SPEED_BOOST if is_panic_mode else 1.0

        # 自然的游荡行为
        if num_nearby_threats == 0:
            base_wander_speed = 0.9  # 安全时游荡速度
        elif num_nearby_threats == 1:
            base_wander_speed = 0.7  # 单威胁时游荡速度
        elif num_nearby_threats == 2:
            base_wander_speed = 0.5  # 双威胁时游荡速度
        else:
            base_wander_speed = 0.3  # 多威胁时游荡速度

        wander_velocity = np.array([math.cos(self.wander_angle), math.sin(self.wander_angle)]) * self.max_speed * base_wander_speed

        # 增强的逃避和战略逃离尝试
        evasion_influence = np.array([0.0, 0.0])
        escape_influence = np.array([0.0, 0.0])

        if nearby_threats:
            # 对每个威胁的个体逃避
            for fuav in nearby_threats:
                dist_to_threat = np.linalg.norm(self.pos - fuav.pos)
                direction_away = (self.pos - fuav.pos) / (dist_to_threat + 1e-6)
                # 距离越近逃避越强
                evasion_strength = ENEMY_EVASION_STRENGTH * (1.0 - dist_to_threat / ENEMY_EVASION_RADIUS_FROM_FRIENDLY)
                evasion_influence += direction_away * evasion_strength

            # 恐慌模式下严格限制的逃离尝试
            if is_panic_mode:
                # 尝试寻找最佳逃离方向但强度降低
                escape_direction = self._find_best_escape_direction(nearby_threats)
                escape_influence = escape_direction * ENEMY_ESCAPE_ATTEMPT_STRENGTH * self.max_speed * 0.7  # 逃离强度系数

                # 在恐慌模式下应用最小速度提升以防止爆发加速
                wander_velocity *= speed_multiplier
                evasion_influence *= speed_multiplier * 0.8  # 恐慌时逃避系数

        edge_repulsion_force = get_map_edge_repulsion(self.pos, self.radius, self.max_speed, MAP_SIZE,
                                                      MAP_EDGE_AVOIDANCE_DIST_ENEMY,
                                                      MAP_EDGE_REPULSION_STRENGTH_ENEMY)

        # 接近边缘时的中心寻求行为
        center_seek_force = np.array([0.0, 0.0])
        map_center = np.array([MAP_SIZE / 2, MAP_SIZE / 2])

        # 检查到各边缘的距离
        dist_to_left = self.pos[0] - self.radius
        dist_to_right = MAP_SIZE - (self.pos[0] + self.radius)
        dist_to_top = self.pos[1] - self.radius
        dist_to_bottom = MAP_SIZE - (self.pos[1] + self.radius)
        min_edge_dist = min(dist_to_left, dist_to_right, dist_to_top, dist_to_bottom)

        if min_edge_dist < ENEMY_CENTER_SEEK_THRESHOLD:
            # 接近边缘时寻求地图中心
            direction_to_center = map_center - self.pos
            dist_to_center = np.linalg.norm(direction_to_center)
            if dist_to_center > 0.1:
                normalized_center_dir = direction_to_center / dist_to_center
                # 越接近边缘中心寻求越强
                strength_factor = (ENEMY_CENTER_SEEK_THRESHOLD - min_edge_dist) / ENEMY_CENTER_SEEK_THRESHOLD
                center_seek_force = normalized_center_dir * self.max_speed * ENEMY_CENTER_SEEK_STRENGTH * strength_factor

        # 基于边缘接近度调整逃避强度 - 更激进的减少
        edge_proximity_factor = 1.0
        if min_edge_dist < ENEMY_CENTER_SEEK_THRESHOLD:
            # 接近边缘时大幅减少逃避
            edge_proximity_factor = min_edge_dist / ENEMY_CENTER_SEEK_THRESHOLD
            edge_proximity_factor = max(0.1, edge_proximity_factor)  # 最小边缘接近因子

        # 应用调整后的逃避影响
        adjusted_evasion_influence = evasion_influence * edge_proximity_factor

        # 增强的力组合与逃离行为
        total_evasion_force = adjusted_evasion_influence + escape_influence

        if np.linalg.norm(total_evasion_force) > 0.05 * self.max_speed:
            # 有威胁时的行为
            if is_panic_mode:
                # 恐慌模式下，主要逃离
                self.vel = (wander_velocity * 0.1 +
                           total_evasion_force * 0.8 +      # 逃离权重
                           edge_repulsion_force * 0.2 +     # 边缘回避权重
                           center_seek_force * 0.1)         # 中心寻求权重
            else:
                # 正常逃避模式
                self.vel = (wander_velocity * 0.3 +
                           total_evasion_force * 0.6 +      # 逃避权重
                           edge_repulsion_force * 0.15 +    # 边缘回避权重
                           center_seek_force * 0.05)        # 中心寻求权重
        else:
            # 安全时主要游荡，轻微避免边缘
            self.vel = (wander_velocity * 0.9 +
                       edge_repulsion_force * 0.15 +        # 边缘回避权重
                       center_seek_force * 0.05)            # 中心寻求权重

        # 应用加速度限制以实现平滑运动
        if dt > 0:
            desired_vel = limit_vector(self.vel, self.max_speed)
            vel_change = desired_vel - self.prev_vel
            max_vel_change = ENEMY_MAX_ACCELERATION * dt

            if np.linalg.norm(vel_change) > max_vel_change:
                vel_change = limit_vector(vel_change, max_vel_change)

            self.vel = self.prev_vel + vel_change
            self.prev_vel = self.vel.copy()
        else:
            self.vel = limit_vector(self.vel, self.max_speed)

        if np.linalg.norm(self.vel) < self.max_speed * 0.05 and dt > 0:
            kick_angle_offset = random.uniform(-math.pi/3, math.pi/3)
            kick_direction = np.array([math.cos(self.wander_angle + kick_angle_offset), math.sin(self.wander_angle + kick_angle_offset)])
            kick_velocity = kick_direction * self.max_speed * ENEMY_MIN_VELOCITY_KICK_FACTOR
            # 对踢击速度也应用加速度限制
            vel_change = kick_velocity
            max_vel_change = ENEMY_MAX_ACCELERATION * dt
            if np.linalg.norm(vel_change) > max_vel_change:
                vel_change = limit_vector(vel_change, max_vel_change)
            self.vel += vel_change
            self.vel = limit_vector(self.vel, self.max_speed)
            self.prev_vel = self.vel.copy()


# 主仿真程序
def main():
    pygame.init()
    screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
    pygame.display.set_caption("Unmanned Vehicle Roundup Simulation")
    clock = pygame.time.Clock()
    small_font = pygame.font.Font(None, 22)
    large_font = pygame.font.Font(None, 60)
    debug_font = pygame.font.Font(None, 18)
    dt = 0

    initial_margin_from_edge = UAV_RADIUS * 8 # 初始边缘距离
    friendly_uavs = [
        FriendlyUAV(initial_margin_from_edge, initial_margin_from_edge, 0),
        FriendlyUAV(MAP_SIZE - initial_margin_from_edge, initial_margin_from_edge, 1),
        FriendlyUAV(MAP_SIZE - initial_margin_from_edge, MAP_SIZE - initial_margin_from_edge, 2),
        FriendlyUAV(initial_margin_from_edge, MAP_SIZE - initial_margin_from_edge, 3)
    ]
    enemy_uav = EnemyUAV(MAP_SIZE / 2, MAP_SIZE / 2)

    current_formation_radius_around_enemy = TARGET_ROUNDUP_RADIUS_AROUND_ENEMY * INITIAL_FORMATION_RADIUS_FACTOR
    formation_shrink_actual_rate = TARGET_ROUNDUP_RADIUS_AROUND_ENEMY * FORMATION_SHRINK_RATE_PER_SEC_FACTOR

    running = True
    mission_accomplished = False
    time_of_completion = 0
    start_time = pygame.time.get_ticks()

    debug_actual_radius_check = False
    debug_shape_check_angular = False
    debug_enemy_velocity_check = False
    debug_target_radius_minimal_check = False
    debug_actual_avg_radius_from_enemy = 0.0
    debug_max_radius_from_enemy = 0.0
    debug_min_radius_from_enemy = 0.0
    debug_angle_gaps = []
    calculated_friendly_formation_center = None # 用于挤压力


    while running:
        current_tick = pygame.time.get_ticks()
        if not mission_accomplished:
            elapsed_time_seconds = (current_tick - start_time) / 1000.0
        else:
            elapsed_time_seconds = time_of_completion

        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_r:
                    initial_margin_from_edge = UAV_RADIUS * 15  # 重置时的边缘距离
                    friendly_uavs = [
                        FriendlyUAV(initial_margin_from_edge, initial_margin_from_edge, 0),
                        FriendlyUAV(MAP_SIZE - initial_margin_from_edge, initial_margin_from_edge, 1),
                        FriendlyUAV(MAP_SIZE - initial_margin_from_edge, MAP_SIZE - initial_margin_from_edge, 2),
                        FriendlyUAV(initial_margin_from_edge, MAP_SIZE - initial_margin_from_edge, 3)
                    ]
                    enemy_uav = EnemyUAV(MAP_SIZE / 2, MAP_SIZE / 2)
                    current_formation_radius_around_enemy = TARGET_ROUNDUP_RADIUS_AROUND_ENEMY * INITIAL_FORMATION_RADIUS_FACTOR
                    mission_accomplished = False
                    time_of_completion = 0
                    start_time = pygame.time.get_ticks()
                    debug_actual_radius_check = False; debug_shape_check_angular = False
                    debug_enemy_velocity_check = False
                    debug_target_radius_minimal_check = False
                    debug_actual_avg_radius_from_enemy = 0.0; debug_max_radius_from_enemy = 0.0
                    debug_min_radius_from_enemy = 0.0; debug_angle_gaps = []
                    calculated_friendly_formation_center = None


        if not mission_accomplished:
            enemy_uav.update(dt, friendly_uavs)

            avg_dist_friendlies_to_enemy = np.mean([np.linalg.norm(f.pos - enemy_uav.pos) for f in friendly_uavs])
            condition_to_shrink = (avg_dist_friendlies_to_enemy < current_formation_radius_around_enemy * 1.7 or \
                                   current_formation_radius_around_enemy > TARGET_ROUNDUP_RADIUS_AROUND_ENEMY * 1.25)

            if condition_to_shrink and current_formation_radius_around_enemy > TARGET_ROUNDUP_RADIUS_AROUND_ENEMY:
                current_formation_radius_around_enemy -= formation_shrink_actual_rate * dt
                current_formation_radius_around_enemy = max(current_formation_radius_around_enemy, TARGET_ROUNDUP_RADIUS_AROUND_ENEMY)
            elif avg_dist_friendlies_to_enemy <= TARGET_ROUNDUP_RADIUS_AROUND_ENEMY * 1.2:
                 current_formation_radius_around_enemy = TARGET_ROUNDUP_RADIUS_AROUND_ENEMY

            # 每帧计算一次友方编队中心，用于挤压力和成功检查
            if friendly_uavs: # 确保列表不为空
                friendly_positions_for_center = np.array([f.pos for f in friendly_uavs])
                calculated_friendly_formation_center = np.mean(friendly_positions_for_center, axis=0)
            else:
                calculated_friendly_formation_center = None


            for fuav in friendly_uavs:
                fuav.update(dt, enemy_uav, friendly_uavs, current_formation_radius_around_enemy, calculated_friendly_formation_center)

            # 任务完成检查 - 以敌方为中心的逻辑
            target_radius_minimal_check = current_formation_radius_around_enemy <= TARGET_ROUNDUP_RADIUS_AROUND_ENEMY * 1.02
            debug_target_radius_minimal_check = target_radius_minimal_check

            if target_radius_minimal_check and friendly_uavs:
                # 使用敌方位置作为包围中心（而非友方编队中心）
                enemy_center = enemy_uav.pos

                # 计算每个友方UAV到敌方中心的距离
                distances_from_enemy = [np.linalg.norm(f.pos - enemy_center) for f in friendly_uavs]
                actual_avg_radius_from_enemy = np.mean(distances_from_enemy)
                max_radius_from_enemy = max(distances_from_enemy)
                min_radius_from_enemy = min(distances_from_enemy)

                # 检查所有友方UAV是否在敌方周围的目标包围半径内
                radius_check = max_radius_from_enemy <= TARGET_ROUNDUP_RADIUS_AROUND_ENEMY * 1.3  # 半径检查容差
                radius_consistency_check = (max_radius_from_enemy - min_radius_from_enemy) <= TARGET_ROUNDUP_RADIUS_AROUND_ENEMY * 0.6  # 编队一致性检查

                debug_actual_radius_check = radius_check and radius_consistency_check
                debug_actual_avg_radius_from_enemy = actual_avg_radius_from_enemy
                debug_max_radius_from_enemy = max_radius_from_enemy
                debug_min_radius_from_enemy = min_radius_from_enemy

                # 检查角度分布（敌方周围的方形编队）
                angles = []
                for f in friendly_uavs:
                    dx = f.pos[0] - enemy_center[0]
                    dy = f.pos[1] - enemy_center[1]
                    angle = math.atan2(dy, dx)
                    if angle < 0:
                        angle += 2 * math.pi
                    angles.append(angle)

                # 排序角度并检查是否大致形成方形（4个象限）
                angles.sort()
                angle_gaps = []
                for i in range(len(angles)):
                    next_angle = angles[(i + 1) % len(angles)]
                    if i == len(angles) - 1:  # 最后到第一个
                        gap = (2 * math.pi - angles[i]) + angles[0]
                    else:
                        gap = next_angle - angles[i]
                    angle_gaps.append(gap)

                # 检查角度是否大致均匀分布
                # 角度检查：确保没有巨大间隙
                max_gap = max(angle_gaps) if angle_gaps else 0
                min_gap = min(angle_gaps) if angle_gaps else 0
                angular_distribution_check = (max_gap - min_gap) < math.pi  # 角度分布检查阈值

                debug_shape_check_angular = angular_distribution_check
                debug_angle_gaps = angle_gaps

                # 敌方速度检查
                enemy_velocity_check = np.linalg.norm(enemy_uav.vel) < enemy_uav.max_speed * ENEMY_TRAPPED_VELOCITY_THRESHOLD_FACTOR
                debug_enemy_velocity_check = enemy_velocity_check

                # 成功条件：所有检查都通过
                if debug_actual_radius_check and debug_shape_check_angular and enemy_velocity_check:
                    mission_accomplished = True
                    time_of_completion = (current_tick - start_time) / 1000.0
                    print(f"MISSION ACCOMPLISHED! Enemy surrounded at {time_of_completion:.2f}s.")
                    print(f"  Enemy Center: ({enemy_center[0]:.1f}, {enemy_center[1]:.1f})")
                    print(f"  Avg Distance to Enemy: {actual_avg_radius_from_enemy:.2f}, Max: {max_radius_from_enemy:.2f}, Min: {min_radius_from_enemy:.2f}")
                    print(f"  Target Encirclement Radius: {TARGET_ROUNDUP_RADIUS_AROUND_ENEMY:.2f}")
                    print(f"  Angular Gaps: {[f'{gap*180/math.pi:.1f}°' for gap in angle_gaps]}")
                    print(f"  Enemy Vel: {np.linalg.norm(enemy_uav.vel):.2f} (Max for capture: {enemy_uav.max_speed * ENEMY_TRAPPED_VELOCITY_THRESHOLD_FACTOR:.1f})")
            else:
                debug_actual_radius_check = False; debug_shape_check_angular = False
                debug_enemy_velocity_check = False
                debug_actual_avg_radius_from_enemy = 0.0; debug_max_radius_from_enemy = 0.0
                debug_min_radius_from_enemy = 0.0; debug_angle_gaps = []


        # --- 绘制 ---
        screen.fill(GRAY)
        pygame.draw.rect(screen, BLACK, (0, 0, MAP_SIZE, MAP_SIZE), 3)

        # 绘制敌方周围的目标包围圈
        if not mission_accomplished:
            # 绘制目标包围半径（蓝色圆圈）
            pygame.draw.circle(screen, (100, 100, 255),
                             (int(enemy_uav.pos[0]), int(enemy_uav.pos[1])),
                             int(TARGET_ROUNDUP_RADIUS_AROUND_ENEMY), 2)

            # 绘制当前编队半径用于比较（浅灰色圆圈）
            pygame.draw.circle(screen, (200, 200, 200),
                             (int(enemy_uav.pos[0]), int(enemy_uav.pos[1])),
                             int(current_formation_radius_around_enemy), 1)

        for fuav in friendly_uavs:
            fuav.draw(screen, small_font)
        enemy_uav.draw(screen, small_font)

        info_text_y_start = 10
        line_height_small = 20
        line_height_debug = 15

        info_text_lines = [
            f"Time: {elapsed_time_seconds:.1f}s",
            f"Target Form. R: {current_formation_radius_around_enemy:.1f}",
        ]
        if not mission_accomplished:
            info_text_lines.append(f"Enemy Vel: ({enemy_uav.vel[0]:.0f}, {enemy_uav.vel[1]:.0f}) L: {np.linalg.norm(enemy_uav.vel):.0f}")
        else:
             info_text_lines.append(f"Enemy Vel (capture): ({enemy_uav.vel[0]:.0f}, {enemy_uav.vel[1]:.0f}) L: {np.linalg.norm(enemy_uav.vel):.0f}")

        for i, line in enumerate(info_text_lines):
            info_surf = small_font.render(line, True, BLACK)
            screen.blit(info_surf, (10, info_text_y_start + i * line_height_small ))

        current_y_offset = info_text_y_start + len(info_text_lines) * line_height_small + 5

        if not mission_accomplished:
            debug_lines = [
                "--- Mission Accomplishment Criteria (Enemy-Centered) ---",
                f"Target R Min: {debug_target_radius_minimal_check}",
                f"Radius OK: {debug_actual_radius_check} (Avg: {debug_actual_avg_radius_from_enemy:.1f}, Max: {debug_max_radius_from_enemy:.1f}, Min: {debug_min_radius_from_enemy:.1f})",
                f"Target Radius: {TARGET_ROUNDUP_RADIUS_AROUND_ENEMY:.1f}",
                f"Angular OK: {debug_shape_check_angular} (Gaps: {[f'{gap*180/math.pi:.0f}°' for gap in debug_angle_gaps] if debug_angle_gaps else 'N/A'})",
                f"Enemy Slow: {debug_enemy_velocity_check} (Vel: {np.linalg.norm(enemy_uav.vel):.1f}, Max: {enemy_uav.max_speed * ENEMY_TRAPPED_VELOCITY_THRESHOLD_FACTOR:.1f})",
            ]
            for i, line in enumerate(debug_lines):
                debug_surf = debug_font.render(line, True, BLACK)
                screen.blit(debug_surf, (10, current_y_offset + i * line_height_debug))

        if mission_accomplished:
            text_surface = large_font.render("Mission accomplished!", True, DARK_GREEN_SUCCESS)
            text_rect = text_surface.get_rect(center=(SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 - 40))
            screen.blit(text_surface, text_rect)

            time_taken_surface = small_font.render(f"Take time: {time_of_completion:.2f} seconds", True, BLACK)
            time_taken_rect = time_taken_surface.get_rect(center=(SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 + 10))
            screen.blit(time_taken_surface, time_taken_rect)

            restart_text_surface = small_font.render("Press 'R' to Restart", True, BLACK)
            restart_text_rect = restart_text_surface.get_rect(center=(SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 + 40))
            screen.blit(restart_text_surface, restart_text_rect)

        pygame.display.flip()

        dt = clock.tick(60) / 1000.0
        dt = max(0.0001, min(dt, 0.05))

    pygame.quit()

if __name__ == '__main__':
    main()
